import { reactive } from 'vue';
import _ from 'lodash';


// useFormSearch方法
export function useFormSearch(ctx, createModel = () => ({})) {
  // FIXME：特殊处理，方便重置reactive对象
  const model = reactive(createModel());

  function search() {
    ctx.emit('search', _.cloneDeep(model));
  }

  function reset() {
    Object.assign(model, createModel());
    search();
  }

  function cancel() {
    ctx.emit('cancel');
  }

  function replace(values) {
    Object.assign(model, _.pick(values, _.keys(model)));
  }

  return {
    model,
    search,
    reset,
    cancel,
    replace,
  };
}
