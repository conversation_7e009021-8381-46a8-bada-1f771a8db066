{"name": "sigs-infoportal-mobi-nodejs", "version": "1.0.0", "private": true, "description": "资讯", "author": "doocom <<EMAIL>>", "license": "ISC", "scripts": {"build:server": "cross-env NODE_ENV=production webpack --config build/webpack.server.prod.js --progress", "build:client": "cross-env NODE_ENV=production cross-env VUE_ENV=client webpack --config build/webpack.client.prod.js --progress", "build": "rimraf ./dist && yarn run build:client && yarn run build:server", "preview": "cross-env NODE_ENV=production ts-node server/server.ts", "preview:inspect": "cross-env NODE_ENV=production node --inspect server/server.ts", "preview:ioredis": "cross-env NODE_ENV=production DEBUG=ioredis:* node --inspect server/server.ts", "serve": "ts-node ./server/server.ts", "serve:inspect": "tsx --inspect --unhandled-rejections=strict server/server.ts", "serve:ioredis": "DEBUG=ioredis:* node --unhandled-rejections=strict server/server.ts", "analyz": "cross-env npm_config_report=1 yarn run build", "lint": "eslint --ext .mjs --ext .js --ext .ts --ext .vue ./src/", "lint:css": "stylelint ./src/**/*.{css,less,vue} --fix"}, "dependencies": {"@panzoom/panzoom": "^4.5.1", "@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^10.9.0", "axios": "^1.6.0", "babel-loader": "^9.1.3", "body-parser": "^1.20.2", "cas-authentication": "^0.0.8", "clean-webpack-plugin": "^4.0.0", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.6", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.33.3", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dotenv": "^16.3.1", "dotenv-webpack": "^8.0.1", "echarts": "^5.4.3", "express": "^4.18.2", "express-session": "^1.17.3", "file-saver": "^2.0.5", "helmet": "^7.1.0", "http-proxy-middleware": "^2.0.6", "ioredis": "^5.3.2", "jquery": "^3.7.1", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "json-bigint": "^1.0.0", "less-loader": "^11.1.3", "lodash": "^4.17.21", "lru-cache": "^10.1.0", "mini-css-extract-plugin": "^2.7.6", "mitt": "^3.0.1", "morgan": "^1.10.0", "nanoid": "^3.3.4", "numeral": "^2.0.6", "p-queue": "^6.6.2", "pinia": "^2.1.7", "postcss-loader": "^7.3.3", "postcss-preset-env": "^9.3.0", "qrcode": "^1.5.1", "qs": "^6.11.2", "redlock": "^5.0.0-beta.2", "regenerator-runtime": "^0.14.0", "sanitize-html": "^2.13.0", "sse.js": "^1.0.0", "style-loader": "^3.3.3", "swiper": "6.8.4", "ts-node": "^10.9.2", "urijs": "^1.19.11", "uuid": "^9.0.1", "vant": "^4.9.3", "vconsole": "^3.15.1", "vue": "^3.5.17", "vue-client-only": "^2.1.0", "vue-i18n": "^9.7.1", "vue-loader": "^17.3.1", "vue-router": "^4.2.5", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0", "weixin-js-sdk": "^1.6.5", "xml2js": "^0.6.2"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.2", "@babel/eslint-parser": "^7.23.3", "@babel/node": "^7.22.19", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-commonjs": "^7.23.3", "@babel/plugin-transform-object-rest-spread": "^7.23.4", "@types/date-fns": "^2.6.0", "@types/express": "^4.17.20", "@types/jquery": "^3.5.30", "@types/lodash": "^4.17.1", "@types/node": "^20.8.10", "@types/urijs": "^1.19.25", "@types/webpack-env": "^1.18.3", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@vue/babel-plugin-jsx": "^1.1.5", "@vue/compiler-sfc": "^3.3.8", "esbuild-loader": "^4.1.0", "eslint": "^8.54.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.6.1", "eslint-import-resolver-webpack": "^0.13.8", "eslint-plugin-import": "^2.29.1", "eslint-plugin-vue": "^9.26.0", "eslint-webpack-plugin": "^4.2.0", "html-webpack-plugin": "^5.5.3", "less": "^4.2.0", "memory-fs": "^0.5.0", "nodemon": "^3.0.2", "postcss": "^8.4.38", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "stylelint": "^15.11.0", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-rational-order-fix": "^0.1.9", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-less": "^2.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-less": "^2.0.0", "stylelint-order": "^6.0.3", "stylelint-webpack-plugin": "^4.1.1", "terser-webpack-plugin": "^5.3.9", "thread-loader": "^4.0.2", "ts-loader": "^9.5.0", "tsconfig-paths": "^4.2.0", "tslib": "^2.6.2", "tsx": "^3.14.0", "typescript": "~5.1.0", "vue-eslint-parser": "^9.3.2", "webpack-bundle-analyzer": "^4.9.1", "webpack-dev-middleware": "^6.1.1", "webpack-hot-middleware": "^2.25.4", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.10.0"}, "engines": {"node": ">=16.18.0"}}