import mitt from 'mitt';


const emitter = mitt<MittEvents>();

function on(event, fn) {
  emitter.on(event, fn);

  return () => emitter.off(event, fn);
}

function emit(event, ...args: unknown[]) {
  // @ts-expect-error: A spread argument must either have a tuple type or be passed to a rest parameter.
  emitter.emit(event, ...args);
}

function clearAll() {
  emitter.all.clear();
}

export default {
  on,
  emit,
  clearAll,
};
