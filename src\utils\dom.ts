export function on(el, event, fn, options) {
  el.addEventListener(event, fn, options);
  return () => el.removeEventListener(event, fn, options);
}

export function e(selector) {
  return document.querySelector(selector);
}

export function appendHTML(element, html) {
  element.insertAdjacentHTML('beforeend', html);
  return element.lastChild;
}

export function appendCss(code) {
  const el = appendHTML(e('head'), `<style>${code}</style>`);
  return () => el.remove();
}

export function vueCssScope(vueVm) {
  const attrIds = vueVm.$el.getAttributeNames();
  for (let i = 0; i < attrIds.length; i += 1) {
    const id = attrIds[i];
    if (id.startsWith('data-v-')) {
      return id;
    }
  }

  return undefined;
}
