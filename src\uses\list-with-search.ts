import { reactive, computed, ref, unref, toRaw } from 'vue';
import { createRecord } from '@/utils/record';

import type { QueryTableParams, QueryTableResult } from '@/types/query-table';

function createPage() {
  return {
    limit: 15,
    page: 1,
  };
}

export function useListWithSearch<
S extends object,
A extends object,
T,
>(params: QueryTableParams<S, A, T>): QueryTableResult<S, A, T> {
  const config = {
    load: () => {},
    simpleSearchModel: {} as S,
    advancedSearchModel: {} as A,
    ...params,
  };

  const simpleSearchModel = reactive({ ...config.simpleSearchModel });
  const advancedSearchModel = reactive({ ...config.advancedSearchModel });

  const searchBarStatus = ref(true);

  const shown = computed(() => unref(searchBarStatus));

  const page = reactive(createPage());

  const table = reactive({
    data: [],
    total: 0,
    offset: 0,
    count: {},
    page: createPage(),
    loading: false,
    error: false,
    finished: false,
    refreshing: false,
    other: {},
  });

  const record = createRecord({ ...table.page, ...unref(shown) ? simpleSearchModel : advancedSearchModel });
  const query = computed<S | A>(() => {
    record.clear();
    record.append(table.page); // 直接使用 table.page
    if (unref(shown)) {
      record.append(simpleSearchModel);
    } else {
      record.append(advancedSearchModel);
    }
    return record.toObj() as S | A;
  });

  function insertData({ data, total, other }, append = false) {
    if (append) {
      table.data = table.data.concat(data);
    } else {
      table.data = data;
    }

    table.total = total;
    table.page.page += 1;
    table.offset = (table.page.page - 1) * table.page.limit;
    table.count = other || {};
    table.finished = table.data.length >= table.total;
    table.other = other;
    table.error = false;
    table.refreshing = false;
  }

  async function loadData(append = false) {
    if (table.loading) return;
    table.loading = true;
    try {
      const data = await config.load({
        ...query.value,
        page: toRaw(table.page),
      });
      insertData(data, append);
    } catch (error) {
      table.error = true;
      throw error;
    } finally {
      table.loading = false;
    }
  }

  function turnPage() {
    loadData(true);
  }

  function load() {
    loadData();
  }

  function search() {
    table.page.page = 1;
    table.error = false;
    table.finished = false;
    table.refreshing = true;
    record.clear();
    record.append(table.page);

    if (unref(searchBarStatus)) {
      record.append(simpleSearchModel);
    } else {
      record.append(advancedSearchModel);
    }

    loadData(false);
  }

  function reset() {
    Object.assign(advancedSearchModel, config.advancedSearchModel);
    search();
  }

  function resetSimple() {
    Object.assign(simpleSearchModel, config.simpleSearchModel);
    search();
  }

  function cancel() {
    Object.assign(advancedSearchModel, config.advancedSearchModel);
    record.append(table.page);

    record.append(simpleSearchModel);

    reset();
  }

  function showSimpleSearch() {
    searchBarStatus.value = true;
    cancel();
  }

  function showAdvancedSearch() {
    searchBarStatus.value = false;
  }

  return {
    shown,
    simpleSearchModel,
    advancedSearchModel,
    table,
    query,
    page,

    load,
    search,
    reset,
    resetSimple,
    turnPage,
    showSimpleSearch,
    showAdvancedSearch,
  };
}
