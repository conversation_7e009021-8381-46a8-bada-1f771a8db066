import { AttachmentVO } from './attachment';

export interface SimpleSearchModel {
  /**
     * 关键字
     */
  keyword?: string;
  /**
     * 类型 数据字典：ARTICLE_TYPE
     */
  type?: string;
}


export interface ArticleVo {
  /**
     * 内容
     */
  content?: string;
  cover?: AttachmentVO;
  /**
     * 主键ID
     */
  id?: number;
  /**
     * 发布时间
     */
  releaseTime?: Date;
  /**
     * 标题
     */
  title?: string;
  /**
     * 类型 数据字典：ARTICLE_TYPE
     */
  type?: string;
  /**
     * 外链
     */
  url?: string;
  /**
     * 是否外链 枚举[是：Y，否：N]
     */
  urlInd?: string;
}
