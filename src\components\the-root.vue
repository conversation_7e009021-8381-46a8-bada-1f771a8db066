<script lang='ts' setup>
import { getCurrentInstance, provide, onBeforeMount, computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import ForbiddenPage from '@/views/forbidden.vue';
import TheTheme from './the-theme.vue';
import TheLayout from './the-layout.vue';
import TheHeader from './the-header.vue';
import ModalAccountError from './biz/modal-account-error.vue';

import { KeepAliveInclude, KeepAliveExclude } from '@/config/keep-alive';
import { useSider } from '@/uses/sider';
import emitter from '@/utils/event-bus';
import { currentLocale } from '@/helps/locale';
import { isCurrentRoot, isCurrentForbidden } from '@/helps/navigation';


const vm = getCurrentInstance();
const locale = currentLocale();
const route = useRoute();
const router = useRouter();
const sider = useSider();

const loaded = ref(false);


provide('menuName', sider.getMenuName);
provide('locale', locale);

const showMenu = computed(() => {
  const { showMenu: isShow } = route.meta ?? {};
  return !!isShow;
});

function goFirstRoute() {
  const firstRoute = sider.getFirstRoute();

  if (firstRoute) {
    router.replace({ name: firstRoute.routeName });
  }
}


function bindEvents() {
  // 公共头部聚合了初始数据，在这里监听数据获取情况
  emitter.on('userDataFetched', ({ error, operations, sider: siderData }) => {
    // TODO: 即使没有权限也放行
    if (error && error?.code !== 'UNAUTHORIZED') {
      return;
    }

    if (Array.isArray(operations) && operations.length > 0) {
      vm.proxy.$setAuths(operations);
    }

    sider.setMenu(siderData);
    if (vm.proxy.$hasAuths() && (isCurrentRoot(router) || isCurrentForbidden(router))) {
      goFirstRoute();
    }

    loaded.value = true;
  });


  const reload = () => {
    window.location.reload();
  };

  // 如果接口出现用户未认证错误，则刷新界面
  emitter.on('userUnauthorizedError', () => {
    reload();
  });

  // 如果刷新Token失败，则刷新界面
  emitter.on('invaildAccessTokenError', () => {
    reload();
  });

  // 如果刷新Token失败，则刷新界面
  emitter.on('invaildRefreshTokenError', () => {
    reload();
  });
}


onBeforeMount(() => {
  bindEvents();
});

const hasRouterAuth = computed(() => {
  const authCodes = route?.meta?.baseAuthCodes as Array<string> || [];

  if (authCodes.length) {
    const routerAuthArr = authCodes.map((item) => {
      return vm.proxy.$can(() => item);
    });

    return routerAuthArr.some((i) => i);
  }
  return true;
});

</script>


<template>
  <TheTheme :locale="locale">
    <TheLayout :has-footer="showMenu">
      <template #header>
        <TheHeader />
      </template>

      <template #main>
        <RouterView
          v-if="$hasAuths() && hasRouterAuth"
          v-slot="{ Component, route: route_ }"
        >
          <KeepAlive
            :include="KeepAliveInclude"
            :exclude="KeepAliveExclude"
          >
            <component
              :is="Component"
              :key="route_.fullPath"
            />
          </KeepAlive>
        </RouterView>
        <ForbiddenPage v-else-if="loaded" />
      </template>
    </TheLayout>

    <ModalAccountError />
  </TheTheme>
</template>
