<script lang='ts' setup>
import { computed } from 'vue';

import { Locale } from '@/config/locale';

const props = withDefaults(defineProps<{
  locale?: Locale
}>(), {
  locale: Locale.ZH_CN,
});


function getThemeClass(currLang: Locale) {
  const classList = ['pima-theme'];
  if (currLang && Object.values(Locale).indexOf(currLang) !== -1) {
    if (currLang === Locale.EN_US) {
      classList.push('pima-chn-lang-theme');
    } else {
      classList.push('pima-eng-lang-theme');
    }
  }

  return classList;
}

const themeClass = computed(() => {
  return getThemeClass(props.locale);
});
</script>


<template>
  <div :class="themeClass">
    <slot />
  </div>
</template>
