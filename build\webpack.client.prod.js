const path = require('path');
const { merge } = require('webpack-merge');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');

const TerserPlugin = require('terser-webpack-plugin');
const baseConfig = require('./webpack.base');
const VueSSRClientPlugin = require('./plugins/vue-ssr-client-plugin');


const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

module.exports = merge(baseConfig, {
  mode: 'production',
  target: ['web', 'es5'],
  entry: {
    app: resolve('../src/entry-client.ts'),
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          format: {
            comments: false,
          },
        },
        extractComments: false,
      }),
      new CssMinimizerPlugin(),
    ],
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        commons: {
          test: /[\\/]src[\\/]/,
          minChunks: 2,
          name: 'commons',
          priority: -1,
        },
        vendors: {
          test: (module) => /node_modules/.test(module.context)
            && !/node_modules\/vue\/dist/.test(module.context)
            && !/node_modules\/core-js\/dist/.test(module.context)
            && !/node_modules\/lodash\/dist/.test(module.context)
            && !/node_modules\/date-fns\/dist/.test(module.context)
            && !/node_modules\/view-ui-plus\/dist/.test(module.context)
            && !/\.css$/.test(module.request),
          name: 'vendors',
          chunks: 'initial',
          priority: -2,
        },
        vue: {
          test: (module) => /node_modules\/vue\//.test(module.context) && !/\.css$/.test(module.request),
          name: 'vue',
          minChunks: 1,
          chunks: 'initial',
          priority: -3,
        },
        coreJs: {
          test: (module) => /node_modules\/core-js\//.test(module.context) && !/\.css$/.test(module.request),
          name: 'core-js',
          minChunks: 1,
          chunks: 'initial',
          priority: -4,
        },
        lodash: {
          test: (module) => /node_modules\/lodash/.test(module.context) && !/\.css$/.test(module.request),
          name: 'lodash',
          minChunks: 1,
          chunks: 'initial',
          priority: -5,
        },
        dateFns: {
          test: (module) => /node_modules\/date-fns\//.test(module.context) && !/\.css$/.test(module.request),
          name: 'date-fns',
          minChunks: 1,
          chunks: 'initial',
          priority: -6,
        },
        viewUiPlus: {
          test: (module) => /node_modules\/view-ui-plus\/dist/.test(module.context) && !/\.css$/.test(module.request),
          name: 'view-ui-plus',
          minChunks: 1,
          chunks: 'initial',
          priority: -7,
        },
      },
    },
    runtimeChunk: {
      name: 'manifest',
    },
  },
  plugins: [
    new CleanWebpackPlugin(),
    new VueSSRClientPlugin(),
  ],
  resolve: {
    alias: {
      // issue: https://github.com/intlify/vue-i18n-next/issues/219
      'vue-i18n': resolve('../node_modules/vue-i18n/dist/vue-i18n.esm-browser.prod.js'),
    },
  },
});
