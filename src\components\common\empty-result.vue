<script lang="ts" setup>
import { namespaceT } from '@/helps/namespace-t';

withDefaults(defineProps<{
  tip?: string;
}>(), {
  tip: namespaceT()('common.hint.notFound'),
});
</script>


<template>
  <div class="empty-result">
    <img
      src="@/assets/img/empty.png"
      alt=""
    >
    <p>{{ tip }}</p>
  </div>
</template>


<style lang="less" scoped>
.empty-result {
  padding: 64px 0;
  color: #777;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;

  img {
    width: 200px;
  }
}
</style>
