import { UnwrapNestedRefs, ComputedRef } from 'vue';

export interface QueryTableParams<S, A, T> {
  load: (query: S | A) => Promise<CommonApiListData<T>>;
  simpleSearchModel?: S;
  advancedSearchModel?: A;
  scrollIntoView?: boolean;
}

export interface Page {
  limit: number;
  page: number;
}

export interface Table<T> {
  data: T[];
  total: number;
  offset: number;
  count: unknown;
  loading: boolean;
  error: boolean;
  finished: boolean;
  refreshing: boolean;
}

export interface QueryTableResult<S = unknown, A = unknown, T = unknown> {
  shown: ComputedRef<boolean>;
  simpleSearchModel: UnwrapNestedRefs<S>;
  advancedSearchModel: UnwrapNestedRefs<A>;
  page: Page;
  table: Table<T>;
  query: ComputedRef<S | A>;

  load: () => void;
  search: () => void;
  reset: () => void;
  resetSimple: () => void;
  turnPage: () => void;
  showSimpleSearch: () => void;
  showAdvancedSearch: () => void;
}
