.pima-layout{
  .van-button {
    font-size: 16px;
    border: 1px solid #E6E6E6;
    border-radius: 6px;
  
    img {
      vertical-align: -5px;
    }
  }
  
  // 主要按钮
  .van-button--primary {
    color: #fff;
    font-weight: 600;
    background: linear-gradient(164deg, #5D2E7F -65.15%, #428CFF 137.86%);
  }
  
  // 默认按钮
  .van-button--default {
    color: #111;
    background-color: #fff;
  }
  
  .van-button--disabled {
    color: #777;
    background-color: #EFEFEF;
    opacity: 1;
  }
  
  .van-button--large {
    height: 48px;
    line-height: 48px;
  }
  
  .van-button--small {
    min-width: 70px;
    height: 30px;
    padding: 0 20px;
    font-weight: 400;
    font-size: 14px;
    line-height: 30px;
    border-radius: 5px;
  }
  
  
  // 单选按钮
  .van-radio__icon--checked .van-icon {
    background-color: #5D2E7F;
    border-color: #5D2E7F;
  }
  
  .van-radio__icon--checked.van-radio__icon--dot {
    border-color: #5D2E7F;
  
    .van-radio__icon--dot__icon {
      background-color: #5D2E7F;
    }
  }
  
  .van-checkbox__icon--checked .van-icon {
    background-color: #5D2E7F;
    border-color: #5D2E7F;
  }
  
  .van-picker__confirm {
    color: #5D2E7F;
  }
  
  .van-text-ellipsis__action {
    color: #428CFF;
  }
  
  // cancader
  .van-cascader__option--selected,
  .van-cascader__selected-icon {
    color: #5D2E7F;
  }
  
  .van-tabs__line {
    width: 16px;
    height: 3px;
    background-color: #5d2e7f;
  }
  
  // 表单
  .van-form {
    .van-cell-group__title {
      padding: 16px 8px 8px;
      color: #777;
      font-weight: 400;
      font-size: 14px;
    }
  
    .van-cell-group {
      overflow: hidden;
      border-radius: 5px;
    }
  
    .van-cell {
      padding: 16px;
  
      .van-field__label {
        margin-bottom: 8px;
        color: #111;
        font-weight: 400;
        font-size: 14px;
      }
  
      .van-field__label--required {
        &::after {
          margin-left: 4px;
          color: #ee0a24;
          content: "*";
        }
  
        &::before {
          display: none;
          content: "";
        }
      }
  
      &::after {
        right: 0;
        border-color: #e7e7e7;
      }
  
      &.rt-16::after {
        right: 16px;
      }
  
      &.no-border::after {
        border: 0;
      }
  
      &.upload-field {
        padding: 0;
      }
    }
  }
  
  // tab
  .pima-tabs.van-tabs {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    background: #fff;
  
    .van-tab {
      color: fade(#111, 90);
      font-size: 14px;
  
      &.van-tab--active {
        color: #5d2e7f;
      }
    }
  
    .van-tabs__line {
      width: 16px;
      height: 3px;
      background-color: #5d2e7f;
    }
  
    &.van-tabs--line .van-tabs__wrap {
      height: 48px;
      border-bottom: 1px solid #ededed;
    }
  }

}


