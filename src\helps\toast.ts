import { showSuccessToast, showFailToast, showLoadingToast } from 'vant';


/**
 * // openToastLoading usage:
 *
 * const close = openToastLoading('loading-text', 1500)
 * ...
 * doSomething() {
 *  ...
 *  ...
 *  close()
 *  // or
 *  close().then(() => {
 *    console.log('loading has been closed')
 *  })
 * }
 */
export function openToastLoading(text, minAliveTime) {
  const destroy = showLoadingToast({
    message: text,
    duration: 0,
  });

  let executed = false;
  let closePromise = null;
  const close = () => {
    if (!executed) {
      executed = true;

      closePromise = new Promise((resolve) => {
        setTimeout(() => {
          destroy();
          resolve();
        }, minAliveTime);
      });
    }

    return closePromise;
  };

  return close;
}

export function openToastError(message) {
  showFailToast({ message });
}

export function openToastSuccess(message, duration = 1.5) {
  showSuccessToast({ message, duration });
}
