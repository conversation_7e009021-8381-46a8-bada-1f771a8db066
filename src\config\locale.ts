export enum Locale {
  ZH_CN = 'zh_CN',
  ZH_HK = 'zh_HK',
  EN_US = 'en_US',
}

const supportLocales: Locale[] = [];
if (process.env.SUPPORT_LOCALES) {
  const locales = process.env.SUPPORT_LOCALES.split(',').map((l: Locale) => l.trim());
  supportLocales.push(...locales);
} else {
  supportLocales.push(Locale.ZH_CN, Locale.ZH_HK, Locale.EN_US);
}

export const SUPPORT_LOCALES = Object.freeze(supportLocales);

// 语言
export const FALLBACK_LOCALE = process.env.FALLBACK_LOCALE || Locale.ZH_CN;
