<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script setup lang="ts">
import { formateToDate } from '@/helps/formate-to-date-type';
import { ArticleVo } from '^/types/info-portal';

interface Props {
  data:ArticleVo
}

const props = withDefaults(defineProps<Props>(), {

});

</script>


<template>
  <div class="article-card">
    <div class="article-info">
      <vanTextEllipsis
        class="title"
        :content="data.title"
        :rows="2"
      />
      <div class="time">
        {{ formateToDate(data.releaseTime) }}
      </div>
    </div>

    <img
      v-if="data.cover"
      class="cover"
      :src="data.cover.filePath.small"
    >
  </div>
</template>


<style lang="less" scoped>

.article-card {
  display: flex;
  gap:12px;
  padding-bottom: 14px;

  &:not(:first-child){
    padding-top: 14px;
    border-top: 1px solid #F5F5F5;
  }

  .article-info{
    display: flex;
    flex:1;
    flex-direction: column;
    gap:2px;

    .title{
      display: flex;
      align-items: center;
      color: #222;
      font-weight: 500;
      font-size: 15px;
      line-height: 21px;
    }

    .time{
      display: flex;
      align-items: center;
      color: #777;
      font-size: 12px;
      line-height: 17px;
    }
  }

  .cover{
    flex-shrink: 0;
    width: 30%;
    aspect-ratio: 106/60;
    object-position: center;
    overflow: hidden;
    border-radius: 6px;
  }

}
</style>
