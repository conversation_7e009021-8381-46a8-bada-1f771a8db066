<script lang='ts' setup>
import { onMounted, computed, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import Swiper from 'swiper';

import { useArticleTypeStore } from '@/store/data-tags/article-type';


interface Props {
  activeType?: string;
}

withDefaults(defineProps<Props>(), {
  activeType: '',
});

const store = useArticleTypeStore();
const { data } = storeToRefs(store);

const columns = computed(() => {
  const allColumns = [{
    title: '全部',
    type: '',
  }];
  const dataColumns = data.value?.map((item) => ({
    title: item.nameByLocale,
    type: item.code,
  }));
  return allColumns.concat(dataColumns || []);
});


const emit = defineEmits(['on-tab']);

function onTabCategory(item) {
  emit('on-tab', item);
}

onMounted(async () => {
  nextTick(async () => {
    await store.loadDataIfNeeded();
    // eslint-disable-next-line no-new
    new Swiper('.category-swiper', {
      direction: 'horizontal',
      slidesPerView: 'auto',
      spaceBetween: 24,
    });
  });
});

</script>


<template>
  <div
    class="category-swiper swiper-container"
  >
    <div class="swiper-wrapper">
      <div
        v-for="(item, idx) in columns"
        :key="`box-${idx}`"
        class="swiper-slide"
        :class="{ active: activeType === item.type }"
      >
        <div
          class="item"
          @click="onTabCategory(item.type)"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
  </div>
</template>


<style lang="less" scoped>
@import url("~/swiper/swiper-bundle.min.css");

.category-swiper {
  padding: 20px 16px 12px;
  background: linear-gradient( #fff , #F9F9FF  90%);


  .swiper-slide {
    width: auto;
    height: 32px;
    color: #444;
    font-weight: 400;
    font-size: 15px;
    letter-spacing: 1px;

    &.active {
      color: #000;
      font-weight: 400;
      font-size: 18px;
    }

    .item {
      position: relative;
      display: flex;
      align-items: end;
      height: 25px;
    }
  }

  .swiper-slide.active::after {
    display: block;
    width: 12px;
    height: 3px;
    margin: 4px auto 0;
    background: #7F51EB;
    border-radius: 2px;
    content: '';
  }
}
</style>
