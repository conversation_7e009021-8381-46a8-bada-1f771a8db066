import { PUBLIC_PATH } from '@/config/public-path';
import { RouterName as RN } from '@/config/router';
import { SiderMenuCodes as SMC } from '@/config/sider-menu';
import TheRoot from '@/components/the-root.vue';
import { namespaceT } from '@/helps/namespace-t';
import { Auth } from '@/config/auth';

const t = namespaceT('common.docTitle');

export const routes = [
  {
    path: PUBLIC_PATH,
    name: RN.Root,
    component: TheRoot,
    children: [
      // 资讯 列表
      {
        path: 'info-portal',
        name: RN.InfoPortal,
        component: () => import('@/views/info-portal'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          activeCodes: [SMC.Information],
          showMenu: false,
          title: t('infoPortal'),
          baseAuthCodes: [Auth.InfoPortal.View],
        },
      },

      // 资讯 详情
      {
        path: 'info-portal/detail',
        name: RN.InfoPortalDetail,
        component: () => import('@/views/info-portal/children/detail'),
        meta: {
          requiresAuth: true, // 不设置或设置为true需要认证
          showMenu: false,
          title: t('detail'),
          baseAuthCodes: [Auth.InfoPortal.View],
        },
      },

      {
        path: 'forbidden',
        name: RN.Forbidden,
        component: () => import('@/views/forbidden.vue'),
      },
    ],
  },
];
