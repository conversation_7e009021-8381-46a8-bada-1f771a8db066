// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
// eslint-disable-next-line import/no-extraneous-dependencies
import '@vue/runtime-core';

import { Auth } from '@/config/auth';

interface DateFormatSTZ {
  (date: string | Date, formatString: string, serverTimezone?: string): string | undefined;
}

interface DateFormat {
  (date: string | Date, remoteFormatString: string, formatString: string): string | undefined;
}

interface JudgeLanguage {
  // eslint-disable-next-line @typescript-eslint/ban-types
  (cnFn: string | Function, enFn: string | Function): string | undefined;
}

interface LoadAuths {
  (): void;
}

interface HasAuths {
  (): boolean;
}

interface SetAuths {
  (auths: Auth[]): void;
}

interface Can {
  (fn: (P: Auth) => string): boolean;
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $dateFormatSTZ: DateFormatSTZ;
    $dateFormat: DateFormat;
    $judgeLanguage: JudgeLanguage;
    $loadAuths: LoadAuths;
    $hasAuths: HasAuths;
    $setAuths: SetAuths;
    $can: Can;
  }
}
