declare global {
  declare module '<PERSON><PERSON>RemoteUI/*';

  interface Window {
    __INITIAL_STATE__: Record<string, unknown>;
  }

  type GetMenuName = {
    (fn: (SM: typeof SiderMenuCodes) => string): string;
  };

  interface Can {
    (fn: (P: typeof Auth) => string): boolean;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  type ValidateField<T extends IDict<any> = string> =
  (prop: T extends string ? string : keyof T, cb?: () => void) => string;

  type FormRefType = InstanceType<typeof Form> & {
    validate: (cb?: (valid?: boolean) => boolean | void) => Promise<boolean>;
    resetFields: () => void;
    validateField: ValidateField;
  };

  // 將部分屬性設為可選
  type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;


  interface BaseSearchModel {
    keyword: string;
  }

  interface People {
    id: number;
    nameCn: string;
    nameEn: string;
    [key: string]: unknown;
  }

  type QueryTableResult<S = unknown, A = unknown, T = unknown> =
    import('@/types/query-table').QueryTableResult<S, A, T>;
}

export {};
