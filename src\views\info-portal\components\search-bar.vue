<script setup lang="ts">
import type { SimpleSearchModel } from '^/types/info-portal';

import { namespaceT } from '@/helps/namespace-t';


const emit = defineEmits<{
  'on-search': [value: string]
}>();

const t = namespaceT('common');

const model = defineModel<SimpleSearchModel>();

const onSearch = (val: string) => {
  emit('on-search', val);
};

</script>

<template>
  <div class="search-bar-container">
    <van-search
      v-model.trim="model.keyword"
      :placeholder="t('placeholder.searching')"
      label-align="left"
      clearable
      @search="onSearch"
      @clear="onSearch"
    >
      <template #left-icon>
        <img
          class="icon-search"
          src="@/assets/img/icon-search.png"
        >
      </template>
    </van-search>
  </div>
</template>

<style lang="less" scoped>
.search-bar-container {
  position: relative;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
  padding: 16px 16px 0;
  background-color: #fff;
  border-radius: 16px 16px 0 0;


  .van-search {
    flex: 1;
    height: 48px;
    padding: 12px 16px;
    background-color: #F2F3F4;
    border: 1px solid #eee;
    border-radius: 12px;

    :deep(.van-search__content) {
      align-items: center;
      padding: 0;
      background-color: #F2F3F4;
    }

    :deep(.van-field__left-icon) {
      display: flex;
      align-items: center;
    }

    :deep(.van-field__control) {
      color: #111;
      font-weight: 500;
      font-size: 16px;

      &::placeholder{
        color:#B9B9B9;
      }
    }
  }

  .icon-search {
    width: 24px;
    height: 24px;
    margin-right: 4px;
  }
}


</style>
