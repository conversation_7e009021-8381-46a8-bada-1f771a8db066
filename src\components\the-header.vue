<script lang='ts' setup>
import { defineAsyncComponent, ref, onMounted } from 'vue';
import URI from 'urijs';

import { PUBLIC_PATH } from '@/config/public-path';
import emitter from '@/utils/event-bus';
import { i18n } from '@/i18n';


// @ts-expect-error: Cannot find module 'pimaRemoteUI/PimaAppHeader' or its corresponding type declarations.
// eslint-disable-next-line import/no-unresolved
const PimaAppHeader = defineAsyncComponent(() => import('pimaRemoteUI/PimaAppHeader'));

const show = ref(false);

onMounted(() => {
  show.value = true;
});

function onClickServiceName() {
  emitter.emit('clickServiceName');
}

function onLogout() {
  const uri = new URI(PUBLIC_PATH);
  uri.segment('logout');
  uri.search({
    service: window.location.href,
    appId: process.env.APP_ID,
    locale: i18n.global.locale,
  });
  window.location.replace(uri.toString());
}

function onUserDataFetched(data) {
  emitter.emit('userDataFetched', data);
}
</script>


<template>
  <PimaAppHeader
    v-if="show"
    @click-service-name="onClickServiceName()"
    @logout="onLogout()"
    @user-data-fetched="onUserDataFetched"
  />
</template>
