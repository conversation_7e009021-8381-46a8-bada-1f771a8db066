<script lang='ts' setup>
import { ref, onMounted, onActivated } from 'vue';
import { useRouter } from 'vue-router';

import { useListWithSearch } from '@/uses/list-with-search';

import { openToastError } from '@/helps/toast';
import SearchBar from './components/search-bar.vue';
import CategorySwiper from './components/category-swiper.vue';
import ArticleList from './components/article-list.vue';

import type { ArticleVo, SimpleSearchModel } from '^/types/info-portal';
import type { PaginationParamsOption } from '@/helps/api';

import { ListApi } from '@/api/info-portal/list';
import { createSimpleSearchModel } from './helps/model';
import { handleListParams } from './helps/handle-api-data';
import { openExternalUrl, push } from '@/helps/navigation';
import { RouterName as RN } from '@/config/router';
import { isY } from '@/helps/y-o-n';


defineOptions({
  name: 'InfoPortal',
});


const router = useRouter();
const backOffset = ref<number>();

const loadData = async (params :SimpleSearchModel & PaginationParamsOption) => {
  try {
    const api = new ListApi();
    api.params = handleListParams(params);
    const res = await api.send();
    return res;
  } catch (error) {
    openToastError(error.message);
    throw error;
  }
};

const list = useListWithSearch({
  load: loadData,
  simpleSearchModel: createSimpleSearchModel(),
});

const onTab = (val: string) => {
  list.simpleSearchModel.type = val;
  list.search();
};

const toDetailPage = (content:ArticleVo) => {
  if (isY(content.urlInd)) {
    openExternalUrl({
      url: content.url,
      isNeedLogin: true,
    });

    return;
  }


  push(router, {
    name: RN.InfoPortalDetail,
    query: {
      id: content.id,
    },
  });
};

onMounted(() => {
  backOffset.value = window.innerHeight / 2;
});

onActivated(() => {
  list.load();
});

</script>


<template>
  <div class="page-home">
    <SearchBar
      v-model="list.simpleSearchModel"
      @on-search="list.search"
    />

    <van-sticky
      :offset-top="0"
    >
      <CategorySwiper
        :active-type="list.simpleSearchModel.type"
        @on-tab="onTab"
      />
    </van-sticky>

    <ArticleList
      v-if="list"
      v-model="list"
      @on-detail="toDetailPage"
    />

    <VanBackTop
      right="16px"
      bottom="82px"
      :offset="backOffset"
      class="home-back-top"
    >
      <img src="@/assets/img/icon-scroll-top.png">
    </VanBackTop>
  </div>
</template>


<style lang="less" scoped>
.page-home {
  /* stylelint-disable-next-line function-no-unknown */
  padding-bottom:  calc(12px + constant(safe-area-inset-bottom));
  padding-bottom:  calc(12px + env(safe-area-inset-bottom));
  background-color: #F9F9FF;
}

.home-header {
  position: relative;
  margin-bottom: 59px;

  .bg {
    width: 100%;
  }
}
</style>

<style lang="less">
.home-back-top {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #FFF;
  border-radius: 110px;
  box-shadow: 0 8px 20px 0 #3700b914;

  > img {
    width: 14px;
    height: 26px;
  }
}
</style>
