/** 基本色 */
// :root
@colorWhite: #fff;
@colorBlack: #000;
@colorBlue: #365aa4;
@colorGreen: #4fad4e;
@colorRed: #d44946;

// grey
@colorGrey100: #171717;
@colorGrey200: #222;
@colorGrey300: #555;
@colorGrey400: #999;
@colorGrey500: #b9b9b9;
@colorGrey600: #dadada;
@colorGrey700: #e4e4e4;
@colorGrey800: #efefef;
@colorGrey900: #f9f9f9;

/** 功能色 */
// primary
@colorPrimary100: #F39800;
@colorPrimary200: #F6B64C;
@colorPrimary300: #F8C166;
@colorPrimary400: #FBE0B2;
@colorPrimary500: #FDF4E5;

// green
@colorGreen100: #009944;
@colorGreen200: #3db271;
@colorGreen300: #7bca9e;
@colorGreen400: #b8e3cb;
@colorGreen500: #f4fff9;

// gold
@colorGold100: #f49b00;
@colorGold200: #f4b655;
@colorGold300: #f4c986;
@colorGold400: #f4dcb7;
@colorGold500: #fff9ef;

// red
@colorRed100: #e63c3c;
@colorRed200: #ff5c5c;
@colorRed300: #ffa8a8;
@colorRed400: #ffc2c2;
@colorRed500: #fff5f5;

/** 文字 */
// 字重
@fontWeightNormal: normal;
@fontWeightBold: bold;

// fontSize
// 大标题
@fontSize30: 30px;
// 标题
@fontSize24: 24px;
// head头部
@fontSize18: 18px;
// Form表单/弹窗标题
@fontSize16: 16px;
// 主要文本
@fontSize14: 14px;
@fontSize12: 12px;
@fontSize11: 11px;
@fontSize10: 10px;

// lineHeight
@lineHeight40: 40px;
@lineHeight38: 38px;
@lineHeight32: 32px;
@lineHeight30: 30px;
@lineHeight26: 26px;
@lineHeight24: 24px;
@lineHeight22: 22px;
@lineHeight20: 20px;
@lineHeight19: 19px;

// fillet圆角
@fillet0: 0;
@fillet1: 1px;
@fillet2: 2px;
@fillet3: 3px;
@fillet4: 4px;

// 一些阴影色
@boxShadowLevel1: 0 2px 5px 0 rgba(0, 0, 0, 0.1);
@boxShadowLevel2: 0 5px 10px -3px rgba(0, 0, 0, 0.3);
@boxShadowLevel3: 0 5px 15px -5px rgba(0, 0, 0, 0.06);

// Select颜色
@selectItemHoverColor: @colorPrimary500;

// 文件上传组件颜色
@attachmentBackgroundHoverColor: #f5f5f5;

// 高级搜索背景颜色
@searchAdvancedBackgroundColor: #f6f6f6;

// 表格颜色
@tableBorderBottom: #c6c6c6;

// 禁止输入背景色
@disabledBackgroundColor: #f4f4f4;

// 滚动条色
@scrollbarThumbColor: #d8d8d8;

// 分辨率
@screenSmMin: 768px;
@screenMdMin: 992px;
@screenLgMin: 1200px;
@screenXlMin: 1680px;

// 头部
@headerHeight: 40px;
@headerBlockLogoWidth: 240px; 

// 侧边栏
@siderWidth: @headerBlockLogoWidth;

// Badge
@badgeColor: @colorRed100;
