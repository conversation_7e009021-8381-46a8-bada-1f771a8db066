import { namespaceT } from '@/helps/namespace-t';
import { AccountError } from '@/consts/account-error';


export function accountErrorI18n(value) {
  const t = namespaceT('accountError');
  const mapper = new Map([
    [AccountError.ACCESS_CHECK, t('accessCheck')],
    [AccountError.ACCOUNT_DISABLE, t('accountDisable')],
    [AccountError.ACCOUNT_FREEZE_OR_DEACTIVATE, t('accountFreezeOrDeactivate')],
    [AccountError.ACCOUNT_EXCEPTION, t('accountException')],
  ]);

  return mapper.get(value) || value;
}
