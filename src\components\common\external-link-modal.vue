
<script setup lang="ts">
import { namespaceT } from '@/helps/namespace-t';
import { copyToClipboard } from '@/helps/clipboard';

interface Props {
  url: string;
}


const props = withDefaults(defineProps<Props>(), {
  url: '',
});


const showModal = defineModel<boolean>();

const t = namespaceT('infoPortal.modal.copy');

const handleCancel = () => {
  showModal.value = false;
};

const handleConfirm = async () => {
  const res = await copyToClipboard(props.url);

  if (res) {
    handleCancel();
  }
};


</script>


<template>
  <VanDialog
    v-model:show="showModal"
    :title="t('title')"
    :confirm-button-text="t('action.copy')"
    class="external-link-modal"
    @confirm="handleConfirm"
  >
    <template #title>
      <div class="title">
        {{ t('title') }}

        <VanIcon
          class="close-btn"
          name="cross"
          @click="handleCancel"
        />
      </div>
    </template>

    {{ t('content') }}
  </VanDialog>
</template>

<style lang="less" scoped>
.external-link-modal:deep(.van-dialog){
  width: 50vw;
  min-width: 300px;
}
</style>
