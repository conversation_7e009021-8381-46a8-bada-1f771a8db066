module.exports = function createMiniprogramRedirect(options) {
  const { serviceUrl, casUrl } = options;
  const validUrl = (url, prefixes) => {
    return url && prefixes.some((prefix) => url.startsWith(prefix));
  };

  return async (req, res, next) => {
    if (!req.xhr) {
      const { casReturnTo, redirectUrl } = req.query;
      if (validUrl(casReturnTo, [serviceUrl])) {
        Object.assign(req.session, {
          cas_return_to: casReturnTo,
        });
      }

      if (validUrl(redirectUrl, [casUrl, serviceUrl])) {
        return res.redirect(redirectUrl);
      }
    }

    return next();
  };
};
