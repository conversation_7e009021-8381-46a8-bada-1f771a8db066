import { openToastSuccess, openToastError } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';

const t = namespaceT('infoPortal');

/**
 * 复制文本到剪贴板 (兼容 SSR)
 * @param text 要复制的文本
 * @param showToast 是否显示提示信息，默认为 true
 * @returns 是否复制成功
 */
export async function copyToClipboard(text: string, showToast = true): Promise<boolean> {
  // 检查是否在浏览器环境
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    if (showToast) {
      openToastError(t('hint.copyFailed'));
    }
    return false;
  }

  try {
    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      if (showToast) {
        openToastSuccess(t('hint.copySuccess'));
      }
      return true;
    }

    // 降级到传统方法
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    const success = document.execCommand('copy');
    document.body.removeChild(textArea);

    if (success && showToast) {
      openToastSuccess(t('hint.copySuccess'));
    } else if (!success && showToast) {
      openToastError(t('hint.copyFailed'));
    }

    return success;
  } catch (error) {
    if (showToast) {
      openToastError(t('hint.copyFailed'));
    }
    return false;
  }
}
