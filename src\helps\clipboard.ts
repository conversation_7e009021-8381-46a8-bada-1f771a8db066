// eslint-disable-next-line import/no-extraneous-dependencies
import copyTextToClipboard from 'copy-text-to-clipboard';
import { openToastSuccess, openToastError } from '@/helps/toast';
import { namespaceT } from '@/helps/namespace-t';

const t = namespaceT('common');

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param showToast 是否显示提示信息，默认为 true
 * @returns 是否复制成功
 */
export function copyToClipboard(text: string, showToast = true): boolean {
  try {
    const success = copyTextToClipboard(text);

    if (success && showToast) {
      openToastSuccess(t('hint.copySuccess'));
    } else if (!success && showToast) {
      openToastError(t('hint.copyFailed'));
    }

    return success;
  } catch (error) {
    if (showToast) {
      openToastError(t('hint.copyFailed'));
    }
    return false;
  }
}
