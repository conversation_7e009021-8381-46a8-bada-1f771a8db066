import { createApp as _createApp } from 'vue';
import { create<PERSON><PERSON>, PiniaVuePlugin } from 'pinia';
import Vant, { Lazyload } from 'vant';
import '@vant/touch-emulator';

import { setupInjectPrototypePlugin } from '@/plugins/inject-prototype';
import { setupAuthPlugin } from '@/plugins/auth';
import { useI18nStore } from '@/store/i18n';
import { PimaRemoteUIPlugin } from '@/plugins/pima-remote-ui';
import { useLoginStatusStore } from '@/store/login-status';
import { i18n } from '@/i18n';
import { createRouter } from '@/router';

import App from '@/app.vue';

import 'vant/lib/index.css';
import '@/styles/app.less';


export function createApp({ locale, isServer, loggedIn }) {
  const app = _createApp(App);
  const pinia = createPinia();

  const loginStatusStore = useLoginStatusStore(pinia);
  loginStatusStore.loggedIn = loggedIn; // 该值的设置必须早于创建 router

  const router = createRouter({ isServer });

  const i18nStore = useI18nStore(pinia);
  i18n.global.locale = locale;
  i18nStore.locale = i18n.global.locale; // server端初始化使用，后续不再使用，直接用i18n

  app.use(i18n);
  app.use(router);
  app.use(PiniaVuePlugin);
  app.use(pinia);
  app.use(PimaRemoteUIPlugin, { i18n });
  app.use(Vant).use(i18n);
  app.use(Lazyload, {
    lazyComponent: true,
  });

  setupInjectPrototypePlugin(app);
  setupAuthPlugin(app);

  return {
    app,
    router,
    pinia,
    i18n,
  };
}
