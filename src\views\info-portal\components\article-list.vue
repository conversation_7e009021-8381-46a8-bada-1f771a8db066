<script setup lang="ts">
import ArticleCard from './article-card.vue';
import PullRefresh from '@/components/common/pull-refresh.vue';
import EmptyResult from '@/components/common/empty-result.vue';

import type { ArticleVo } from '^/types/info-portal';
import type { QueryTableResult } from '^/types/query-table';

import { namespaceT } from '@/helps/namespace-t';

const emit = defineEmits<{
  'on-detail':[content: ArticleVo]
}>();

const list = defineModel<QueryTableResult<any, any, ArticleVo>>();

const tm = namespaceT('common');

const onDetail = (content: ArticleVo) => {
  emit('on-detail', content);
};

</script>


<template>
  <PullRefresh
    v-model="list.table.refreshing"
    @refresh="list.search"
  >
    <van-list
      :loading="list.table.loading"
      :error="list.table.error"
      :error-text="tm('hint.requestFailed')"
      :finished="list.table.finished"
      class="pm-list article-list"
      @load="list.turnPage"
    >
      <ArticleCard
        v-for="content in list.table.data"
        :key="content.id"
        :data="content"
        @click="onDetail(content)"
      />

      <template #finished>
        <span v-if="list.table.total > 0">
          {{ tm('hint.noMoreData') }}
        </span>

        <EmptyResult
          v-else
          :tip="tm('hint.notFound')"
        />
      </template>
    </van-list>
  </PullRefresh>
</template>


<style lang="less" scoped>
.article-list {
  display: flex;
  flex-direction: column;
  margin:12px 16px;
  padding: 12px;
  background-color: #fff;
  border-radius: 12px;
}
</style>
