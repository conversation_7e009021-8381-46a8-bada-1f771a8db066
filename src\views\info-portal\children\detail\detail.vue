<script setup lang="ts">
import { reactive, ref, onBeforeMount } from 'vue';
import { useRoute } from 'vue-router';

import PimaSanitizeHtml from '@/components/common/pima-sanitize-html';


import { DetailApi } from '@/api/info-portal/detail';
import { openToastError } from '@/helps/toast';

import { createArticleModel } from '../../helps/model';
import { useArticleTypeStore } from '@/store/data-tags/article-type';
import { formateToDate } from '@/helps/formate-to-date-type';
import { namespaceT } from '@/helps/namespace-t';


const route = useRoute();
const store = useArticleTypeStore();
const t = namespaceT('infoPortal');

const loading = ref(false);
const model = reactive(createArticleModel());


const fetchDetail = async () => {
  try {
    loading.value = true;
    const api = new DetailApi({ id: route.query.id });
    const res = await api.sendWithSpecifyType();
    Object.assign(model, res);
  } catch (error) {
    openToastError(error.message);
  } finally {
    loading.value = false;
  }
};


onBeforeMount(() => {
  store.loadDataIfNeeded();
  fetchDetail();
});

</script>


<template>
  <div class="page-article-detail">
    <div class="article-header">
      <div class="article-title">
        {{ model.title }}
      </div>

      <div class="article-info">
        <div class="type">
          {{ store.getTextByCode(model.type) }}
        </div>

        <div class="time">
          {{ formateToDate(model.releaseTime) }}
        </div>
      </div>
    </div>

    <div class="article-content">
      <PimaSanitizeHtml
        class="view"
        :inner-html="model.content"
      />
    </div>

    <VanDivider class="divider">
      {{ t('text.end') }}
    </VanDivider>
  </div>
</template>


<style lang="less" scoped>
.page-article-detail {
  padding: 16px;

  .article-header {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f5f5f5;


    .article-title {
      margin-bottom: 12px;
      color: #2c2c2c;
      font-weight: 600;
      font-size: 22px;
      line-height: 31px;
    }

    .article-info {
      display: flex;
      align-items: center;
      font-size: 12px;
      line-height: 17px;

      .type {
        margin-right: 6px;
        padding-right: 6px;
        color: #222;
        border-right: 1px solid #d9d9d9;
      }

      .time {
        color: #555;
      }
    }
  }

  .divider{
    width: 30vw;
    margin: 16px auto;
  }
}

</style>
