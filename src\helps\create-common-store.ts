import { defineStore } from 'pinia';
import _ from 'lodash';

// import { textByLocale } from '@/helps/locale';

// function createDataItemNameByLocale(data) {
//   if (_.isEmpty(data)) {
//     return [];
//   }

//   const res = data.map((item) => ({
//     ...item,
//     nameByLocale: textByLocale(item.name, item.enName),
//   }));

//   return res;
// }

export function createCommonStore(storeName, DataTagApi, options = {}) {
  const initState = {
    data: null,
    loading: false,

    ...options.state,
  };

  const getters = {
    getTextByCode(state) {
      return function getTextByCode(code) {
        const match = _.find(state.data || [], { code });
        return match ? match.nameByLocale : null;
      };
    },

    ...options.getters,
  };

  const actions = {
    async loadDataIfNeeded() {
      if (this.loading || this.data !== null) {
        return;
      }

      this.loading = true;
      try {
        const api = new DataTagApi();
        const data = await api.send();
        this.data = data;
      } catch (error) {
        this.data = null;
        throw error;
      } finally {
        this.loading = false;
      }
    },

    ...options.actions,
  };

  return defineStore(storeName, {
    state: () => initState,
    actions,
    getters,
  });
}
