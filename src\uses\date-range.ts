import { isValid } from 'date-fns';
import { isBeforeDay, isAfterDay } from '@/helps/date';


/**
 * @typedef { Object } GetMinAndMax
 * @property { Function } min
 * @property { Function } max
 */

/**
 * 返回2个disabledDate函数，用于限制最小和最大日期选择
 * @param { GetMinAndMax } range - 获取选择的时间范围
 * @param { GetMinAndMax } limit - 获取限制的时间范围
 */
export function useDateRange(range, limit) {
  // 检查当前日期是否小于最小日期
  function isBeforeMinDate(date, getMin) {
    if (typeof getMin !== 'function') {
      return false;
    }

    const min = getMin();
    return isValid(min) && isBeforeDay(date, min);
  }

  // 检查当前日期是否大于最大日期
  function isAfterMaxDate(date, getMax) {
    if (typeof getMax !== 'function') {
      return false;
    }

    const max = getMax();
    return isValid(max) && isAfterDay(date, max);
  }

  return {
    minDisabled(date) {
      return isBeforeMinDate(date, limit?.min) || isAfterMaxDate(date, limit?.max) || isAfterMaxDate(date, range.max);
    },

    maxDisabled(date) {
      return isBeforeMinDate(date, limit?.min) || isAfterMaxDate(date, limit?.max) || isBeforeMinDate(date, range.min);
    },
  };
}
