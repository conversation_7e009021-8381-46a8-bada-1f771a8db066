import 'core-js/stable';
import 'regenerator-runtime/runtime';
import { createApp } from './main';
import VConsole from 'vconsole';


const piniaStateValue = window.__INITIAL_STATE__;
const { locale } = piniaStateValue.i18n as { locale: string };
const { loggedIn } = piniaStateValue.loginStatus as { loggedIn: boolean };
const { app, router, pinia } = createApp({ locale, isServer: false, loggedIn });
pinia.state.value = piniaStateValue;


if (process.env.DEBUG) {
  const vConsole = new VConsole();
  vConsole.showSwitch();
}


router.isReady().then(() => {
  app.mount('#app');
});
