@import url("./utils.less");
@import url("./vant.less");


ul,
ol {
  list-style-type: none;
}

.main {
  position: relative;
  background-color: #fff;

}

.body-has-footer {
  padding-bottom: 62px;

  .main {
    min-height: calc(100vh - 62px);
  }
}

.footer {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;

  .menu {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    background: fade(#fff, 95%);
    filter: drop-shadow(0 -1px 4px fade(#000, 8));
    backdrop-filter: blur(11px);

    li {
      position: relative;
      width: 100%;
      text-align: center;

      a {
        position: relative;
        display: block;
        padding: 2px;
      }

      img {
        display: block;
        height: 24px;
        margin: 0 auto;
      }

      .title {
        color: #111;
        font-size: 10px;
      }

      .badge {
        position: absolute;
        top: -4px;
        left: 50%;
        width: 16px;
        height: 16px;
        margin-left: 3px;
        color: #fff;
        line-height: 16px;
        text-align: center;
        background-color: #D54941;
        border-radius: 50%;

        .badge-count {
          display: block;
          font-size: 80%;
          line-height: 16px;
        }
      }

      a.check-in {
        position: absolute;
        top: -22px;
        left: 50%;
        width: 58px;
        height: 58px;
        padding: 4px;
        background-color: fade(#fff, 95%);
        border-radius: 50%;
        transform: translateX(-50%);

        img {
          width: 50px;
          height: 50px;
          filter: drop-shadow(0 0 8px fade(#5D2E7F, 16));
        }
      }

      &.selected {
        .title {
          color: #5D2E7F;
        }
      }
    }
  }
}

.view {
  img {
    max-width: 100%;
  }

  .list-paddingleft-2 {
    padding-left: 30px;
  }
}

.textarea-view {
  white-space: pre-wrap;
  word-break: break-word;
}


.main,
.page-home,
.page-article-detail {
  /* stylelint-disable-next-line function-no-unknown */
  min-height: calc(100vh - constant(safe-area-inset-bottom));
  min-height: calc(100vh - env(safe-area-inset-bottom));
}

.page-article-detail {
  /* stylelint-disable-next-line function-no-unknown */
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom));
}