<script lang="ts" setup>
import { Loading } from 'vant';

withDefaults(defineProps<{ inline?: boolean }>(), {
  inline: false,
});
</script>


<template>
  <div
    :class="{'loading-container': !inline}"
  >
    <Loading
      size="24px"
      vertical
    >
      {{ $t('common.hint.loading') }}
    </Loading>
  </div>
</template>


<style lang="less" scoped>
.loading-container {
  padding: 100px 0;
}
</style>
