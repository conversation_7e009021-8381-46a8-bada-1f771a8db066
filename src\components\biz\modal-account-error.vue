<template>
  <van-popup
    v-model:show="showQuitModal"
    :width="400"
    :close-on-click-overlay="false"
    class-name="pima-modal quit-modal"
    footer-hide
  >
    <div class="modal-main">
      <div class="modal-title">
        <img
          src="@/assets/img/icon-error.png"
          alt=""
        >
        <span>{{ accountErrorI18n(accountErrorCode) }}</span>
      </div>
      <van-button
        type="primary"
        class="pima-btn btn-quit"
        @click="quit"
      >
        {{ $t('common.action.confirm') }}
      </van-button>
    </div>
  </van-popup>
</template>


<script lang='ts'>
import { defineComponent, ref, onBeforeMount } from 'vue';

import { PUBLIC_PATH } from '@/config/public-path';
import emitter from '@/utils/event-bus';
import { accountErrorI18n } from '@/helps/i18n/account-error';


export default defineComponent({
  name: 'ModalAccountError',

  serverCacheKey() {
    return 'ModalAccountError';
  },

  setup() {
    const showQuitModal = ref(false);
    const accountErrorCode = ref('');


    onBeforeMount(() => {
      emitter.on('catchAccountError', (errorCode) => {
        showQuitModal.value = true;
        accountErrorCode.value = errorCode;
      });
    });


    function quit() {
      const redirect = encodeURIComponent(window.location.href);
      window.location.href = `${PUBLIC_PATH}logout?service=${redirect}`;
    }


    return {
      showQuitModal,
      accountErrorCode,

      accountErrorI18n,
      quit,
    };
  },
});
</script>


<style lang="less">
.quit-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }

  .modal-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    .modal-title {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 0 20px;
      color: #ff5c5c;
      font-size: 18px;

      img {
        margin-right: 20px;
      }
    }

    .btn-quit {
      width: 120px;
      height: 38px;
      font-size: 14px;
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      border-radius: 2px;

      &:hover {
        background-color: var(--primary-hover-color);
        border-color: var(--primary-hover-color);
      }
    }
  }
}
</style>
