export const SStore = {
  get(key) {
    const s = sessionStorage.getItem(key);
    const o = JSON.parse(s);
    return o;
  },

  set(key, value) {
    const s = JSON.stringify(value);
    sessionStorage.setItem(key, s);
  },

  has(key) {
    const value = SStore.get(key);
    return ![null, undefined].includes(value);
  },

  remove(key) {
    sessionStorage.removeItem(key);
  },
};

export const LStore = {
  get(key) {
    const s = localStorage.getItem(key);
    const o = JSON.parse(s);
    return o;
  },

  set(key, value) {
    const s = JSON.stringify(value);
    localStorage.setItem(key, s);
  },

  has(key) {
    const value = LStore.get(key);
    return ![null, undefined].includes(value);
  },

  remove(key) {
    localStorage.removeItem(key);
  },
};
