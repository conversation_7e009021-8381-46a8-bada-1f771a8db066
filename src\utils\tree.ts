import _ from 'lodash';


export function* forestNodes(forest = []) {
  for (let i = 0; i < forest.length; i += 1) {
    const treeNode = forest[i];
    yield treeNode;
    yield* forestNodes(treeNode.children ?? []);
  }
}

export function renameNodes(tree, oldNodeName, newNodeName) {
  if (Array.isArray(tree)) {
    return tree.map((oldNode) => {
      if ([oldNodeName] in oldNode) {
        const newNode = _.cloneDeep(oldNode);
        const children = renameNodes(newNode[oldNodeName], oldNodeName, newNodeName);
        if (children.length > 0) {
          newNode[newNodeName] = children;
        }

        delete newNode[oldNodeName];
        return newNode;
      }

      return oldNode;
    });
  }

  return [];
}
