import _ from 'lodash';

import type { PaginationParamsOption } from '@/helps/api';
import type { SimpleSearchModel } from '^/types/info-portal';
import { isNothing } from '@/utils/is';

type ListParamsType = PaginationParamsOption & SimpleSearchModel;

export const handleListParams = (params :ListParamsType) => {
  const cloneData = _.cloneDeep(params);

  cloneData.type = isNothing(cloneData.type) ? undefined : cloneData.type;

  return cloneData;
};
