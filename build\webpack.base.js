const path = require('path');
const { VueLoaderPlugin } = require('vue-loader');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const Dotenv = require('dotenv-webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const { DefinePlugin } = require('webpack');
const { ModuleFederationPlugin } = require('webpack').container;

require('./env-config');


const resolve = (...args) => {
  return path.resolve(__dirname, ...args);
};

const dotenvFilePath = () => {
  const f = process.env.NODE_ENV === 'test' ? '.env.test' : '.env';
  return resolve('../', f);
};

let publicPath = process.env.PUBLIC_PATH || '/';
if (publicPath.lastIndexOf('/') !== publicPath.length - 1) {
  publicPath += '/';
}

const config = {
  output: {
    path: resolve('../dist'), // 出口文件路径
    filename: 'static/js/[name].[chunkhash].js',
    chunkFilename: 'static/js/[name].[chunkhash].js',
    publicPath,
  },
  cache: {
    type: 'filesystem',
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: [
          'thread-loader',
          {
            loader: 'esbuild-loader',
            options: {
              loader: 'js',
              target: 'es2018', // 设置编译目标为 es2018
            },
          },
        ],
      },
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: [
          'thread-loader',
          {
            loader: 'esbuild-loader',
            options: {
              loader: 'ts',
              target: 'es2018', // 设置编译目标为 es2018
            },
          },
        ],
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        type: 'asset',
        generator: {
          filename: 'static/assets/img/[name][ext]',
        },
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        type: 'asset/resource',
        generator: {
          filename: 'static/assets/fonts/[name][ext]',
        },
      },
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader',
          {
            loader: 'esbuild-loader',
            options: {
              loader: 'css',
            },
          },
        ],
      },
      {
        test: /\.vue$/,
        use: {
          loader: 'vue-loader',
          options: {
            hotReload: false,
          },
        },
      },
      {
        test: /\.less$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader',
          {
            loader: 'esbuild-loader',
            options: {
              loader: 'css',
            },
          },
          {
            loader: 'less-loader',
            options: {
              lessOptions: {
                javascriptEnabled: true,
              },
            },
          },
        ],
      },
    ],
  },
  plugins: [
    new DefinePlugin({
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
    }),
    new VueLoaderPlugin(),
    new Dotenv({
      path: dotenvFilePath(),
    }),
    new MiniCssExtractPlugin({
      filename: 'static/css/[name].[contenthash].css',
      chunkFilename: 'static/css/[name].[contenthash].css',
      ignoreOrder: true,
    }),
    new CopyWebpackPlugin({
      // from后的路径是相对于项目的根目录，to后的路径是相对于打包后的dist目录
      patterns: [{ from: './public/', to: './static' }],
    }),
    new ModuleFederationPlugin({
      name: 'pimaRemoteUI',
      filename: 'remoteEntry.js',
      remotes: {
        pimaRemoteUI: `pimaRemoteUI@${process.env.REMOTE_UI_ENTRY_URL}`,
      },
      shared: {
        vue: {
          strictVersion: true,
          requiredVersion: false,
          singleton: true,
        },
        '@vue/compiler-sfc': {
          strictVersion: true,
          requiredVersion: false,
          singleton: true,
        },
      },
    }),
  ],
  resolve: {
    extensions: ['.js', '.ts', '.vue', '.less', '.mjs'], // 引入省略后缀，LESS为部分依赖库会不使用.less后缀
    alias: {
      '@': resolve('../src'),
      '~': resolve('../node_modules'),
    },
  },
};

if (process.env.report) {
  const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
  config.plugins.push(new BundleAnalyzerPlugin());
}

module.exports = config;
