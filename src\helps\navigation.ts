import _ from 'lodash';
import URI from 'urijs';
import { showImagePreview } from 'vant';
import type { LocationQueryRaw, LocationQueryValueRaw, RouteLocationNormalized, Router } from 'vue-router';
import { RouterName as RN, FROM_PARAM_NAME_KEY, FROM_ROUTE_NAME_KEY } from '@/config/router';
import { useLoginStatusStore } from '@/store/login-status';
import { MiniprogramPages } from '@/consts/miniprogram-pages';

import type { ExternalUrlType } from '^/types/external-url-type';

/**
 * 在路由跳转前处理，将来源路由名称存入目标路由的meta信息。
 *
 * @param to - 即将导航到的目标路由对象。
 * @param from - 当前导航正离开的路由对象。
 * @param next - 调用以进行下一步导航的函数。
 */
export function beforeEach(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: (vm?: RouteLocationNormalized) => void,
) {
  Object.assign(to.meta, {
    [FROM_ROUTE_NAME_KEY]: from.name,
  });

  // 调用next函数传递处理后的to路由
  next();
}

/**
 * 检查当前是否处于应用的根路由。
 *
 * @param router - Vue Router实例。
 */
export function isCurrentRoot(router: Router) {
  return RN.Root === router.currentRoute.value.name;
}

/**
 * 导航至应用的根路由。
 *
 * @param router - Vue Router实例。
 */
export function backToRoot(router: Router) {
  router.replace({ name: RN.Root });
}

/**
 * 判断当前是否处于禁止访问的页面。
 *
 * @param router - Vue Router实例。
 */
export function isCurrentForbidden(router: Router) {
  return RN.Forbidden === router.currentRoute.value.name;
}

/**
 * 导航至禁止访问的页面。
 *
 * @param router - Vue Router实例。
 */
export function goForbidden(router: Router) {
  router.replace({ name: RN.Forbidden });
}

/**
 * 判断是否可以执行浏览器后退操作。
 *
 * @param router - Vue Router实例。
 */
export function canGoBack(router: Router) {
  const from = _.get(router.currentRoute.value.query, FROM_PARAM_NAME_KEY, null);
  return !_.isNil(from);
}

/**
 * 执行浏览器后退或根据条件进行路由替换。
 *
 * @param router - Vue Router实例。
 */
export function goBack(router: Router) {
  const fromRouteName = _.get(
    router.currentRoute,
    `meta.${FROM_ROUTE_NAME_KEY}`,
  );
  const queryFromRouteName = _.get(
    router.currentRoute,
    `query.${FROM_PARAM_NAME_KEY}`,
    null,
  );
  // 会记录fromRouteName在route的meta里，如果fromRouteName是null/Root，则表示是当前为浏览器打开的第一个URL
  // 如果不是第一个URL，则可以直接操作浏览器后退
  if (![null, RN.Root].includes(fromRouteName)) {
    router.back();
    // 如果是第一个URL，无法正常操作浏览后退，则使用replace方法替换，需指定queryFromRouteName才生效
  } else if (!_.isNil(queryFromRouteName)) {
    router.replace({
      name: queryFromRouteName,
    });
  }
}

/**
 * 向历史记录中添加新路由并导航。
 *
 * @param router - Vue Router实例。
 * @param name - 目标路由的名称。
 * @param query - 路由的查询参数，默认为空对象。
 */
export const push = (router: Router, { name, query = {}, params = {} }: {
  name: string, query?: LocationQueryRaw, params?: LocationQueryRaw,
}) => {
  router.push({
    name,
    query: {
      ...query,
      [FROM_PARAM_NAME_KEY]: router.currentRoute.value.name as LocationQueryValueRaw,
    },
    params,
  });
};

/**
 * 替换当前路由。
 *
 * @param router - Vue Router实例。
 * @param name - 新路由的名称。
 * @param query - 路由的查询参数，默认为空对象。
 */
export const replace = (router: Router, name: string, query: LocationQueryRaw = {}) => {
  router.replace({
    name,
    query,
  });
};


// 处理登录跳转
function handleLoginRedirect(ops: ExternalUrlType) {
  const store = useLoginStatusStore();
  const { loggedIn } = store;

  let urlString = ops.url;
  if (ops.isNeedLogin) {
    if (loggedIn) {
      urlString = URI(process.env.CAS_BASE_URL)
        .segment('login')
        .addQuery('service', ops.url)
        .toString();
    } else {
      const uri = new URI(window.location.href);
      uri.addQuery('toUrl', ops.url);
      urlString = new URI(process.env.PUBLIC_PATH || '/')
        .segment('login')
        .addQuery('returnTo', uri.toString())
        .toString();

      Object.assign(ops, { wxPage: MiniprogramPages.LOGIN });
    }
  } else {
    Object.assign(ops.params, { hasToken: false });
  }

  return { urlString, wxPage: ops.wxPage };
}

/**
 * 打开第三方小程序
 *
 * @param url - 示例：https://url.com/?openMiniProgram=true&appId=wxd46033a6xxxxxxxx&path=pages/home/<USER>
 */
function getMiniprogramUrl(url: string) {
  const uri = new URI(url);
  const { appId, path, openMiniProgram } = uri.query(true);
  const openMiniProgramUri = new URI(MiniprogramPages.OPEN_MINIPROGRAM);
  openMiniProgramUri.addQuery({
    appId,
    path,
  });

  return { openMiniProgram, openMiniProgramUri };
}


export function openExternalUrl(options: ExternalUrlType) {
  const IMAGE_EXT = ['png', 'jpg', 'jpeg', 'gif'];
  const COMPRESSED_EXT = ['zip', 'rar'];

  if (!options.url) return;
  const ops = {
    params: {},
    wxPage: MiniprogramPages.WEBVIEW,
    ...options,
  };

  const { openMiniProgram, openMiniProgramUri } = getMiniprogramUrl(options.url);
  const { urlString, wxPage } = handleLoginRedirect(ops);


  const params = {
    ...ops.params,
    url: urlString,
    defaultUrl: options.url,
  };

  const wxUri = new URI(wxPage);
  wxUri.addQuery(params);

  if (options.wxPage === MiniprogramPages.DOWNLOAD) {
    if (options.file?.fileName) wxUri.addQuery('title', options.file.fileName);
    if (options.file?.fileExt) wxUri.addQuery('fileExt', options.file.fileExt);
  }


  let isMiniProgram = false;
  // eslint-disable-next-line import/no-extraneous-dependencies
  const wx = require('weixin-js-sdk');
  if (wx.miniProgram) {
    wx.miniProgram.getEnv((res) => {
      if (res.miniprogram) {
        isMiniProgram = true;
      }
    });
  }
  if (isMiniProgram) {
    const routerType = ops.routerType || 'navigateTo';

    // 图片文件预览
    if (options.file?.fileExt && IMAGE_EXT.includes(options.file.fileExt)) {
      showImagePreview({
        images: [options.url],
        closeable: true,
      });
      return;
    }

    // 压缩包文件弹出提示
    if (options.file?.fileExt && COMPRESSED_EXT.includes(options.file.fileExt)) {
      options.zipCallback?.();
      return;
    }

    wx.miniProgram[routerType]({
      url: openMiniProgram ? openMiniProgramUri.toString() : wxUri.toString(),
    });
  } else {
    window.open(options.url, options.target || '_self');
  }
}
