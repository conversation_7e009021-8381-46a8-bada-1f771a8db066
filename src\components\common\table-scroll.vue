<template>
  <div class="table-scroll">
    <div class="table-wrapper">
      <slot />
    </div>
    <div class="paginator-wrap">
      <slot name="paginator" />
    </div>
  </div>
</template>


<style scoped lang="less">
.table-scroll {
  padding: 10px 20px 0;
  // padding-top: 10px;

  .table-wrapper {
    // overflow: auto;

    :deep(& > *) {

      @w-sider: 172px;
      @w-padding: 40px;
      @w-scroll: 18px;
      // min-width: calc(1920px - @w-sider - @w-padding - @w-scroll);
      // 可以这样写, 但是每个 table column 要设置 minWidth
      min-width: calc(100vw - @w-sider - @w-padding - @w-scroll);
    }
  }

  .paginator-wrap {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    // 防止被下载悬浮框遮挡
    margin-bottom: 60px;
  }
}
</style>
