{"extends": ["stylelint-config-standard-vue", "stylelint-config-standard-less", "stylelint-config-rational-order-fix", "stylelint-config-standard"], "plugins": ["stylelint-less", "stylelint-order", "stylelint-config-rational-order-fix/plugin"], "overrides": [{"files": ["**/*.vue"], "customSyntax": "postcss-html"}], "rules": {"font-family-no-missing-generic-family-keyword": null, "no-empty-source": null, "function-no-unknown": [true, {"ignoreFunctions": ["/^fade$/"]}], "selector-pseudo-element-no-unknown": [true, {"ignorePseudoElements": ["v-deep"]}], "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["deep"]}], "keyframes-name-pattern": null, "selector-class-pattern": null, "less/no-duplicate-variables": null, "no-descending-specificity": null, "no-duplicate-selectors": null}}