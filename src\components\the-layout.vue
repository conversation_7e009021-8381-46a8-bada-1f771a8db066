<script lang='ts' setup>
withDefaults(defineProps<{
  hasFooter?: boolean,
}>(), {
  hasFooter: true,
});

</script>


<template>
  <div class="pima-layout">
    <div
      v-show="false"
      class="header"
    >
      <slot name="header" />
    </div>
    <div
      class="body"
      :class="{ 'body-has-footer': hasFooter }"
    >
      <div class="main">
        <slot name="main" />
      </div>
    </div>
    <div class="footer">
      <slot name="footer" />
    </div>
  </div>
</template>
